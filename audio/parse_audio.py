import os
import GPUtil  # Add this import for GPU utility
import threading  # Add threading for thread-safe initialization

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from loguru import logger
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
import soundfile

from pydantic import BaseModel
from typing import Any

from dotenv import load_dotenv

load_dotenv()


def checkAudioEnabled():
    # Check if audio parsing is enabled
    if os.getenv("ENABLE_PARSE_AUDIO", "false").lower() != "true":
        logger.warning(
            "Audio parsing is disabled. Set ENABLE_PARSE_AUDIO=true to enable it.")
        # 剩下的不执行，但不报错
        return


checkAudioEnabled()

# Schema Definitions


class RecognitionRequest(BaseModel):
    input: str


class RecognitionResponse(BaseModel):
    output: Any


# Lock for thread-safe initialization
_pipeline_lock = threading.Lock()
_inference_pipeline = None  # Lazy initialization for inference pipeline


def get_inference_pipeline():
    global _inference_pipeline
    if _inference_pipeline is not None:
        return _inference_pipeline  # Return if already initialized

    with _pipeline_lock:  # Ensure thread-safe initialization
        if _inference_pipeline is not None:
            return _inference_pipeline  # Double-check inside the lock

        _inference_pipeline = pipeline(
            task=Tasks.auto_speech_recognition,
            model='iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch',
            vad_model='iic/speech_fsmn_vad_zh-cn-16k-common-pytorch',
            vad_kwargs={"max_single_segment_time": 60000},
            punc_model='iic/punc_ct-transformer_cn-en-common-vocab471067-large',
            lm_model='iic/speech_transformer_lm_zh-cn-common-vocab8404-pytorch',
            # Use selected GPUs
            device=f"cuda:2",
            batch_size=10,
            batch_size_s=300,
            batch_size_threshold_s=60,
            return_raw_text=True,
            sentence_timestamp=True,
            return_spk_res=False,
            disable_update=True,
        )
        return _inference_pipeline


router = APIRouter()

# Endpoints


@router.post(
    "",
    response_model=RecognitionResponse,
    tags=["audio"],
    summary="Parse audio files and perform speech recognition"
)
async def parse_audio(request: RecognitionRequest) -> RecognitionResponse:
    logger.info("request: {}", request)
    pipeline = get_inference_pipeline()  # Use lazy initialization
    res = pipeline(request.input)
    return RecognitionResponse(output=res)


class StreamingRecognitionResponse(BaseModel):
    key: str
    text: str


streaming_inference_pipeline = pipeline(
    task=Tasks.auto_speech_recognition,
    model='iic/speech_paraformer_asr_nat-zh-cn-16k-common-vocab8404-online',
    punc_model='iic/punc_ct-transformer_cn-en-common-vocab471067-large',
    device=f"cuda:{gpu_ids}",
    model_revision='v2.0.4',
    disable_update=True,
)


async def stream_recognition_v2(request: RecognitionRequest):
    logger.info("request: {}", request)

    speech, sample_rate = soundfile.read(request.input)
    speech_length = speech.shape[0]

    sample_offset = 0
    chunk_size = [5, 10, 5]
    encoder_chunk_look_back = 4
    decoder_chunk_look_back = 1
    stride_size = chunk_size[1] * 960
    cache = {}
    is_final = False

    for sample_offset in range(0, speech_length, min(stride_size, speech_length - sample_offset)):
        if sample_offset + stride_size >= speech_length - 1:
            stride_size = speech_length - sample_offset
            is_final = True

        res = streaming_inference_pipeline(
            speech[sample_offset: sample_offset + stride_size],
            cache=cache,
            is_final=is_final,
            encoder_chunk_look_back=encoder_chunk_look_back,
            decoder_chunk_look_back=decoder_chunk_look_back
        )
        if len(res) == 0:
            break

        logger.info("res: {}", res[0]["text"])
        yield f"data: {res}\n\n"


@router.post(
    "/streaming",
    tags=["audio"],
    summary="Perform streaming speech recognition"
)
async def streaming_recognition_v2(request: RecognitionRequest) -> StreamingResponse:
    return StreamingResponse(stream_recognition_v2(request), media_type="application/json")
