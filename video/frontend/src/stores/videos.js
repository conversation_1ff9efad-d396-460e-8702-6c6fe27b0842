import { defineStore } from 'pinia'
import { ref } from 'vue'
import client from '@/api/client'

export const useVideoStore = defineStore('videos', () => {
  // 状态
  const videos = ref([])
  const currentVideo = ref(null)

  // 操作
  const fetchVideos = async () => {
    try {
      const response = await client.get('/videos')
      videos.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch videos:', error)
      throw error
    }
  }

  const fetchVideo = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}`)
      currentVideo.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch video:', error)
      throw error
    }
  }

  const startAnalysis = async (videoId) => {
    try {
      const response = await client.post(`/videos/${videoId}/analyze`)
      
      // 更新本地视频状态
      const video = videos.value.find(v => v.id === videoId)
      if (video) {
        video.status = 'analyzing'
      }
      
      if (currentVideo.value && currentVideo.value.id === videoId) {
        currentVideo.value.status = 'analyzing'
      }
      
      return response
    } catch (error) {
      console.error('Failed to start analysis:', error)
      throw error
    }
  }

  const getAnalysisResults = async (videoId) => {
    try {
      const response = await client.get(`/analysis/${videoId}`)
      return response
    } catch (error) {
      console.error('Failed to get analysis results:', error)
      throw error
    }
  }

  const getAnalysisStep = async (videoId, step) => {
    try {
      const response = await client.get(`/analysis/${videoId}/step/${step}`)
      return response
    } catch (error) {
      console.error('Failed to get analysis step:', error)
      throw error
    }
  }

  // 获取场景检测结果
  const getScenes = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}/scenes`)
      return response
    } catch (error) {
      console.error('Failed to get scenes:', error)
      throw error
    }
  }

  // 触发场景检测
  const detectScenes = async (videoId, options = {}) => {
    try {
      const response = await client.post(`/videos/${videoId}/scenes/detect`, options)
      return response
    } catch (error) {
      console.error('Failed to detect scenes:', error)
      throw error
    }
  }

  // 获取比特率统计
  const getBitrateStats = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}/bitrate-stats`)
      return response
    } catch (error) {
      console.error('Failed to get bitrate stats:', error)
      throw error
    }
  }

  // 获取比特率图表
  const getBitratePlot = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}/bitrate-plot`)
      return response
    } catch (error) {
      console.error('Failed to get bitrate plot:', error)
      throw error
    }
  }

  // 重试视频分析
  const retryAnalysis = async (videoId) => {
    try {
      const response = await client.post(`/videos/${videoId}/retry`)

      // 更新本地视频状态
      const video = videos.value.find(v => v.id === videoId)
      if (video) {
        video.status = 'analyzing'
      }

      if (currentVideo.value && currentVideo.value.id === videoId) {
        currentVideo.value.status = 'analyzing'
      }

      return response
    } catch (error) {
      console.error('Failed to retry analysis:', error)
      throw error
    }
  }

  return {
    // 状态
    videos,
    currentVideo,

    // 操作
    fetchVideos,
    fetchVideo,
    startAnalysis,
    getAnalysisResults,
    getAnalysisStep,
    getScenes,
    detectScenes,
    getBitrateStats,
    getBitratePlot,
    retryAnalysis
  }
})
